<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Socialite facade manually
        $loader = \Illuminate\Foundation\AliasLoader::getInstance();
        $loader->alias('Socialite', \Laravel\Socialite\Facades\Socialite::class);

        // Bind Socialite Factory
        $this->app->singleton(\Laravel\Socialite\Contracts\Factory::class, function ($app) {
            return new \Laravel\Socialite\SocialiteManager($app);
        });
    }
}
