<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\GoogleController;

Route::get('/', function () {
    return view('welcome');
});

// Admin Access Test Route
Route::get('/admin-test', function () {
    return view('admin-access-test');
})->name('admin.test');

// Debug Authentication Route
Route::get('/debug-auth', function () {
    $data = [
        'laravel_auth_check' => auth()->check(),
        'laravel_auth_user' => auth()->user(),
        'session_has_user' => session()->has('user'),
        'session_user' => session()->get('user'),
        'session_id' => session()->getId(),
        'all_session_data' => session()->all(),
    ];

    return response()->json($data, 200, [], JSON_PRETTY_PRINT);
})->name('debug.auth');

// Quick Login Test Route
Route::get('/quick-login', function () {
    $credentials = ['email' => '<EMAIL>', 'password' => 'password'];

    if (Auth::attempt($credentials)) {
        $user = Auth::user();
        session()->put('user', [
            'id' => $user->user_id,
            'name' => $user->name,
            'email' => $user->email,
            'role' => $user->role
        ]);

        return redirect()->route('admin.dashboard')->with('success', 'Logged in successfully as ' . $user->name);
    } else {
        return redirect('/admin-test')->with('error', 'Login failed');
    }
})->name('quick.login');

// Authentication Routes
Route::middleware('session.guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

// Google OAuth Routes
Route::get('/auth/google', [GoogleController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [GoogleController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// Simple OAuth Test Route
Route::get('/test-oauth-simple', function () {
    try {
        // Create the OAuth URL manually to see what's happening
        $clientId = config('services.google.client_id');
        $redirectUri = config('services.google.redirect');
        $scope = 'openid email profile';
        $state = \Illuminate\Support\Str::random(40);

        $params = [
            'client_id' => $clientId,
            'redirect_uri' => $redirectUri,
            'scope' => $scope,
            'response_type' => 'code',
            'state' => $state,
            'access_type' => 'offline',
            'prompt' => 'consent'
        ];

        $authUrl = 'https://accounts.google.com/o/oauth2/v2/auth?' . http_build_query($params);

        return response()->json([
            'status' => 'Manual OAuth URL Generation',
            'config_check' => [
                'client_id' => $clientId,
                'client_id_length' => strlen($clientId),
                'redirect_uri' => $redirectUri,
                'redirect_uri_valid' => filter_var($redirectUri, FILTER_VALIDATE_URL),
            ],
            'generated_auth_url' => $authUrl,
            'test_instructions' => [
                'step_1' => 'Copy the generated_auth_url above',
                'step_2' => 'Paste it in a new browser tab',
                'step_3' => 'See what specific error Google shows',
                'step_4' => 'Report back the exact error message'
            ],
            'common_errors' => [
                'invalid_client' => 'Client ID is wrong or not found',
                'redirect_uri_mismatch' => 'Redirect URI not configured in Google Console',
                'access_blocked' => 'App not approved or user not added as test user',
                'unauthorized_client' => 'OAuth client not properly configured'
            ]
        ], 200, [], JSON_PRETTY_PRINT);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500, [], JSON_PRETTY_PRINT);
    }
})->name('test.oauth.simple');

// Debug OAuth Route
Route::get('/debug-oauth', function () {
    try {
        $socialite = \Laravel\Socialite\Facades\Socialite::driver('google');

        // Get the redirect URL that would be generated
        $redirectUrl = $socialite->redirect()->getTargetUrl();

        return response()->json([
            'status' => 'OAuth Debug Information',
            'config' => [
                'client_id' => config('services.google.client_id'),
                'redirect_uri' => config('services.google.redirect'),
                'app_url' => config('app.url'),
                'current_host' => request()->getSchemeAndHttpHost(),
            ],
            'generated_url' => $redirectUrl,
            'url_parts' => parse_url($redirectUrl),
            'troubleshooting' => [
                'if_still_getting_invalid_request' => [
                    'step_1' => 'Check OAuth Consent Screen is configured',
                    'step_2' => 'Add your email as a test user',
                    'step_3' => 'Enable People API in Google Console',
                    'step_4' => 'Wait 10-15 minutes after making changes',
                    'step_5' => 'Try in incognito/private browser window',
                    'step_6' => 'Check if app is in testing mode (not production)',
                ]
            ],
            'required_google_console_setup' => [
                'oauth_consent_screen' => 'Must be configured with app name and contact email',
                'test_users' => 'Add your email address as a test user',
                'apis_to_enable' => [
                    'People API',
                    'Google Identity Services API'
                ],
                'redirect_uris' => [
                    request()->getSchemeAndHttpHost() . '/auth/google/callback',
                    'http://localhost:8008/auth/google/callback',
                    'http://127.0.0.1:8008/auth/google/callback',
                ],
                'javascript_origins' => [
                    request()->getSchemeAndHttpHost(),
                    'http://localhost:8008',
                    'http://127.0.0.1:8008',
                ]
            ]
        ], 200, [], JSON_PRETTY_PRINT);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500, [], JSON_PRETTY_PRINT);
    }
})->name('debug.oauth');

// Google OAuth Test Route
Route::get('/test-google-oauth', function () {
    try {
        // Test if we can use Socialite facade
        $socialiteWorks = true;
        $socialiteError = null;

        // Test creating a driver
        $driver = \Laravel\Socialite\Facades\Socialite::driver('google');
        $driverWorks = true;

        // Get the actual redirect URL that will be generated
        $redirectUrl = $driver->getTargetUrl();
    } catch (\Exception $e) {
        $socialiteWorks = false;
        $socialiteError = $e->getMessage();
        $driverWorks = false;
        $redirectUrl = 'Error generating URL';
    }

    $config = [
        'google_client_id' => env('GOOGLE_CLIENT_ID'),
        'google_client_secret' => env('GOOGLE_CLIENT_SECRET') ? 'Set (hidden)' : 'Not set',
        'google_redirect_uri' => env('GOOGLE_REDIRECT_URI'),
        'app_url' => env('APP_URL'),
        'current_url' => request()->getSchemeAndHttpHost(),
        'expected_callback' => request()->getSchemeAndHttpHost() . '/auth/google/callback',
        'socialite_class_exists' => class_exists('Laravel\Socialite\SocialiteManager'),
        'socialite_facade_exists' => class_exists('Laravel\Socialite\Facades\Socialite'),
        'socialite_works' => $socialiteWorks,
        'driver_works' => $driverWorks,
        'socialite_error' => $socialiteError,
        'google_service_config' => config('services.google'),
        'generated_redirect_url' => $redirectUrl,
    ];

    return response()->json([
        'status' => 'Google OAuth Configuration Check',
        'config' => $config,
        'ready' => $socialiteWorks && $driverWorks && !empty($config['google_client_id']) && !empty(env('GOOGLE_CLIENT_SECRET')),
        'google_console_setup' => [
            'required_redirect_uris' => [
                $config['expected_callback'],
                'http://localhost:8008/auth/google/callback',
                'http://127.0.0.1:8008/auth/google/callback',
            ],
            'required_javascript_origins' => [
                $config['current_url'],
                'http://localhost:8008',
                'http://127.0.0.1:8008',
            ]
        ]
    ], 200, [], JSON_PRETTY_PRINT);
})->name('test.google.oauth');

// Google Account Management (for authenticated users)
Route::middleware('session.auth')->group(function () {
    Route::get('/auth/google/link', [GoogleController::class, 'linkGoogleAccount'])->name('auth.google.link');
    Route::post('/auth/google/unlink', [GoogleController::class, 'unlinkGoogleAccount'])->name('auth.google.unlink');
});

Route::middleware('session.auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Profile Settings Routes
    Route::get('/profile', function () {
        return view('profile.settings');
    })->name('profile.settings');

    Route::post('/profile', function () {
        // Validate the request
        $validated = request()->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'current_password' => 'nullable|string',
            'new_password' => 'nullable|string|min:6|confirmed',
            'timezone' => 'required|string',
            'language' => 'required|string',
            'notifications_email' => 'boolean',
            'notifications_browser' => 'boolean',
            'notifications_marketing' => 'boolean',
        ]);

        // Get current user data
        $user = session('user');

        // Update user session data (in a real app, this would update the database)
        $user['name'] = $validated['name'];
        $user['email'] = $validated['email'];

        // Store updated user data in session
        session(['user' => $user]);

        return redirect()->route('profile.settings')->with('success', 'Profile updated successfully!');
    })->name('profile.update');

    // My Questions Routes
    Route::get('/questions', function () {
        // Sample questions data (in a real app, this would come from database)
        $questions = [
            [
                'id' => 1,
                'title' => 'Introduction to Machine Learning',
                'description' => 'Questions generated from ML fundamentals document',
                'question_count' => 15,
                'difficulty' => 'Intermediate',
                'category' => 'Technology',
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(1),
                'status' => 'completed',
                'source_type' => 'PDF Document',
                'source_name' => 'ml_fundamentals.pdf',
                'tags' => ['machine-learning', 'ai', 'algorithms'],
                'questions' => [
                    ['question' => 'What is supervised learning?', 'type' => 'multiple_choice', 'difficulty' => 'easy'],
                    ['question' => 'Explain the difference between classification and regression.', 'type' => 'short_answer', 'difficulty' => 'medium'],
                    ['question' => 'What are the main types of machine learning algorithms?', 'type' => 'multiple_choice', 'difficulty' => 'easy'],
                ]
            ],
            [
                'id' => 2,
                'title' => 'JavaScript ES6 Features',
                'description' => 'Modern JavaScript concepts and syntax',
                'question_count' => 12,
                'difficulty' => 'Beginner',
                'category' => 'Programming',
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(3),
                'status' => 'completed',
                'source_type' => 'Web Article',
                'source_name' => 'ES6 Tutorial - MDN',
                'tags' => ['javascript', 'es6', 'programming'],
                'questions' => [
                    ['question' => 'What is arrow function syntax?', 'type' => 'multiple_choice', 'difficulty' => 'easy'],
                    ['question' => 'How do template literals work?', 'type' => 'short_answer', 'difficulty' => 'medium'],
                ]
            ],
            [
                'id' => 3,
                'title' => 'Digital Marketing Strategy',
                'description' => 'Comprehensive guide to modern marketing',
                'question_count' => 8,
                'difficulty' => 'Advanced',
                'category' => 'Business',
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(7),
                'status' => 'processing',
                'source_type' => 'Text Input',
                'source_name' => 'Marketing Strategy Notes',
                'tags' => ['marketing', 'strategy', 'business'],
                'questions' => []
            ],
            [
                'id' => 4,
                'title' => 'React Hooks Deep Dive',
                'description' => 'Advanced React hooks patterns and best practices',
                'question_count' => 20,
                'difficulty' => 'Advanced',
                'category' => 'Programming',
                'created_at' => now()->subHours(6),
                'updated_at' => now()->subHours(2),
                'status' => 'completed',
                'source_type' => 'Video Transcript',
                'source_name' => 'React Hooks Masterclass',
                'tags' => ['react', 'hooks', 'frontend'],
                'questions' => [
                    ['question' => 'When should you use useCallback?', 'type' => 'multiple_choice', 'difficulty' => 'hard'],
                    ['question' => 'Explain the useEffect cleanup function.', 'type' => 'essay', 'difficulty' => 'medium'],
                ]
            ],
            [
                'id' => 5,
                'title' => 'Data Structures Fundamentals',
                'description' => 'Core data structures and algorithms',
                'question_count' => 25,
                'difficulty' => 'Intermediate',
                'category' => 'Computer Science',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(8),
                'status' => 'completed',
                'source_type' => 'PDF Document',
                'source_name' => 'data_structures_guide.pdf',
                'tags' => ['data-structures', 'algorithms', 'computer-science'],
                'questions' => [
                    ['question' => 'What is the time complexity of binary search?', 'type' => 'multiple_choice', 'difficulty' => 'medium'],
                    ['question' => 'Compare arrays vs linked lists.', 'type' => 'essay', 'difficulty' => 'medium'],
                ]
            ]
        ];

        return view('questions.index', compact('questions'));
    })->name('questions.index');

    Route::get('/questions/{id}', function ($id) {
        // Sample question detail (in a real app, this would come from database)
        $question = [
            'id' => $id,
            'title' => 'Introduction to Machine Learning',
            'description' => 'Questions generated from ML fundamentals document',
            'question_count' => 15,
            'difficulty' => 'Intermediate',
            'category' => 'Technology',
            'created_at' => now()->subDays(2),
            'updated_at' => now()->subDays(1),
            'status' => 'completed',
            'source_type' => 'PDF Document',
            'source_name' => 'ml_fundamentals.pdf',
            'tags' => ['machine-learning', 'ai', 'algorithms'],
            'questions' => [
                [
                    'id' => 1,
                    'question' => 'What is supervised learning?',
                    'type' => 'multiple_choice',
                    'difficulty' => 'easy',
                    'options' => [
                        'A type of learning where the algorithm learns from labeled data',
                        'A type of learning where the algorithm learns without any data',
                        'A type of learning where the algorithm supervises other algorithms',
                        'A type of learning that requires human supervision at all times'
                    ],
                    'correct_answer' => 0,
                    'explanation' => 'Supervised learning uses labeled training data to learn a mapping from inputs to outputs.'
                ],
                [
                    'id' => 2,
                    'question' => 'Explain the difference between classification and regression.',
                    'type' => 'short_answer',
                    'difficulty' => 'medium',
                    'sample_answer' => 'Classification predicts discrete categories or classes, while regression predicts continuous numerical values.'
                ],
                [
                    'id' => 3,
                    'question' => 'What are the main types of machine learning algorithms?',
                    'type' => 'multiple_choice',
                    'difficulty' => 'easy',
                    'options' => [
                        'Supervised, Unsupervised, and Reinforcement Learning',
                        'Linear and Non-linear algorithms',
                        'Simple and Complex algorithms',
                        'Fast and Slow algorithms'
                    ],
                    'correct_answer' => 0,
                    'explanation' => 'The three main paradigms of machine learning are supervised, unsupervised, and reinforcement learning.'
                ]
            ]
        ];

        return view('questions.show', compact('question'));
    })->name('questions.show');
});

// Admin Routes
Route::middleware(['admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', function () {
        // Enhanced admin dashboard data (in a real app, this would come from database)
        $stats = [
            'total_users' => 1247,
            'active_users' => 892,
            'users_growth' => 12.5,
            'total_questions' => 15634,
            'questions_today' => 156,
            'questions_week' => 1834,
            'total_documents' => 3421,
            'documents_processing' => 23,
            'processing_rate' => 94.2,
            'monthly_revenue' => 24750,
            'revenue_growth' => 18.3,
            'revenue_target' => 30000,
            'new_users_today' => 23,
            'server_uptime' => '99.9%',
            'storage_used' => '67%'
        ];

        $recentUsers = [
            ['id' => 1, 'name' => 'John Smith', 'email' => '<EMAIL>', 'created_at' => now()->subHours(2), 'status' => 'active'],
            ['id' => 2, 'name' => 'Sarah Johnson', 'email' => '<EMAIL>', 'created_at' => now()->subHours(5), 'status' => 'active'],
            ['id' => 3, 'name' => 'Mike Wilson', 'email' => '<EMAIL>', 'created_at' => now()->subHours(8), 'status' => 'pending'],
            ['id' => 4, 'name' => 'Emily Davis', 'email' => '<EMAIL>', 'created_at' => now()->subDay(), 'status' => 'active'],
            ['id' => 5, 'name' => 'David Brown', 'email' => '<EMAIL>', 'created_at' => now()->subDays(2), 'status' => 'inactive'],
        ];

        $recentActivity = [
            ['user' => 'John Smith', 'action' => 'Created question set', 'details' => 'Machine Learning Basics', 'time' => now()->subMinutes(15), 'type' => 'question'],
            ['user' => 'Sarah Johnson', 'action' => 'Uploaded document', 'details' => 'data_science.pdf', 'time' => now()->subMinutes(32), 'type' => 'document'],
            ['user' => 'Mike Wilson', 'action' => 'Registered account', 'details' => 'New user signup', 'time' => now()->subHours(1), 'type' => 'user'],
            ['user' => 'Emily Davis', 'action' => 'Generated questions', 'details' => '25 questions from React documentation', 'time' => now()->subHours(2), 'type' => 'question'],
            ['user' => 'David Brown', 'action' => 'Updated profile', 'details' => 'Changed notification settings', 'time' => now()->subHours(3), 'type' => 'user'],
            ['user' => 'Lisa Anderson', 'action' => 'Uploaded document', 'details' => 'react_tutorial.pdf', 'time' => now()->subHours(4), 'type' => 'document'],
        ];

        $systemHealth = [
            ['service' => 'Web Server', 'status' => 'healthy', 'uptime' => '99.9%', 'response_time' => '45ms'],
            ['service' => 'Database', 'status' => 'healthy', 'uptime' => '99.8%', 'response_time' => '12ms'],
            ['service' => 'File Storage', 'status' => 'healthy', 'uptime' => '99.9%', 'response_time' => '23ms'],
            ['service' => 'AI Service', 'status' => 'warning', 'uptime' => '98.5%', 'response_time' => '156ms'],
            ['service' => 'Email Service', 'status' => 'healthy', 'uptime' => '99.7%', 'response_time' => '89ms'],
            ['service' => 'Cache Server', 'status' => 'healthy', 'uptime' => '99.6%', 'response_time' => '8ms'],
        ];

        // Chart data for user growth and question generation
        $chartData = [
            'user_growth' => [
                'Mon' => 45,
                'Tue' => 52,
                'Wed' => 38,
                'Thu' => 61,
                'Fri' => 73,
                'Sat' => 29,
                'Sun' => 41,
            ],
            'questions_generated' => [
                'Mon' => 234,
                'Tue' => 287,
                'Wed' => 195,
                'Thu' => 342,
                'Fri' => 398,
                'Sat' => 156,
                'Sun' => 223,
            ],
        ];

        // Performance metrics
        $performance = [
            'cpu' => 34,
            'memory' => 67,
            'disk' => 45,
            'network' => 23,
            'database' => 28,
        ];

        // Quick stats
        $quickStats = [
            'online_users' => 127,
            'avg_session' => '8m 32s',
            'bounce_rate' => 23,
            'conversion_rate' => 4.7,
            'mrr' => 24.8,
            'support_tickets' => 3,
        ];

        return view('admin.index', compact('stats', 'recentUsers', 'recentActivity', 'systemHealth', 'chartData', 'performance', 'quickStats'));
    })->name('index');

    // Main Admin Dashboard with CRUD functionality (using controller)
    Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');

    // Users Management
    Route::get('/users', function () {
        $users = [
            ['id' => 1, 'name' => 'John Smith', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'active', 'created_at' => now()->subDays(30), 'last_login' => now()->subHours(2), 'questions_count' => 45],
            ['id' => 2, 'name' => 'Sarah Johnson', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'active', 'created_at' => now()->subDays(25), 'last_login' => now()->subHours(5), 'questions_count' => 32],
            ['id' => 3, 'name' => 'Mike Wilson', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'pending', 'created_at' => now()->subHours(8), 'last_login' => null, 'questions_count' => 0],
            ['id' => 4, 'name' => 'Emily Davis', 'email' => '<EMAIL>', 'role' => 'moderator', 'status' => 'active', 'created_at' => now()->subDays(60), 'last_login' => now()->subDay(), 'questions_count' => 128],
            ['id' => 5, 'name' => 'David Brown', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'inactive', 'created_at' => now()->subDays(90), 'last_login' => now()->subWeek(), 'questions_count' => 12],
            ['id' => 6, 'name' => 'Lisa Anderson', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'active', 'created_at' => now()->subDays(15), 'last_login' => now()->subHours(12), 'questions_count' => 67],
            ['id' => 7, 'name' => 'Tom Garcia', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'active', 'created_at' => now()->subDays(45), 'last_login' => now()->subHours(1), 'questions_count' => 89],
            ['id' => 8, 'name' => 'Anna Martinez', 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'suspended', 'created_at' => now()->subDays(20), 'last_login' => now()->subDays(3), 'questions_count' => 23],
        ];

        return view('admin.users', compact('users'));
    })->name('users');

    // System Settings
    Route::get('/settings', function () {
        return view('admin.settings');
    })->name('settings');

    // Analytics
    Route::get('/analytics', function () {
        return view('admin.analytics');
    })->name('analytics');
});

// Enhanced Admin CRUD Routes (protected by admin middleware)
Route::middleware(['admin'])->prefix('admin')->name('admin.')->group(function () {
    // Enhanced Dashboard functionality now integrated into main dashboard
    Route::get('/system-health', [App\Http\Controllers\Admin\DashboardController::class, 'systemHealth'])->name('system-health');
    Route::get('/export-data', [App\Http\Controllers\Admin\DashboardController::class, 'exportData'])->name('export-data');
    Route::get('/reports', [App\Http\Controllers\Admin\DashboardController::class, 'reports'])->name('reports');

    // User Management CRUD
    Route::resource('users-crud', App\Http\Controllers\Admin\UserController::class)->names([
        'index' => 'users-crud.index',
        'create' => 'users-crud.create',
        'store' => 'users-crud.store',
        'show' => 'users-crud.show',
        'edit' => 'users-crud.edit',
        'update' => 'users-crud.update',
        'destroy' => 'users-crud.destroy',
    ]);
    Route::patch('users-crud/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users-crud.toggle-status');

    // Subject Management CRUD
    Route::resource('subjects', App\Http\Controllers\Admin\SubjectController::class);
    Route::get('subjects/{subject}/statistics', [App\Http\Controllers\Admin\SubjectController::class, 'statistics'])->name('subjects.statistics');

    // Note Management CRUD
    Route::resource('notes-crud', App\Http\Controllers\Admin\NoteController::class)->names([
        'index' => 'notes-crud.index',
        'create' => 'notes-crud.create',
        'store' => 'notes-crud.store',
        'show' => 'notes-crud.show',
        'edit' => 'notes-crud.edit',
        'update' => 'notes-crud.update',
        'destroy' => 'notes-crud.destroy',
    ]);
    Route::patch('notes-crud/bulk-update-status', [App\Http\Controllers\Admin\NoteController::class, 'bulkUpdateStatus'])->name('notes-crud.bulk-update-status');

    // Question Management CRUD
    Route::resource('questions', App\Http\Controllers\Admin\QuestionController::class);

    // Answer Management CRUD
    Route::resource('answers', App\Http\Controllers\Admin\AnswerController::class);
    Route::patch('answers/{answer}/toggle-correctness', [App\Http\Controllers\Admin\AnswerController::class, 'toggleCorrectness'])->name('answers.toggle-correctness');
    Route::patch('answers/bulk-update-correctness', [App\Http\Controllers\Admin\AnswerController::class, 'bulkUpdateCorrectness'])->name('answers.bulk-update-correctness');

    // User Profile Management CRUD
    Route::resource('user-profiles', App\Http\Controllers\Admin\UserProfileController::class);
    Route::get('user-profiles-statistics', [App\Http\Controllers\Admin\UserProfileController::class, 'statistics'])->name('user-profiles.statistics');

    // Feedback Management CRUD
    Route::resource('feedback', App\Http\Controllers\Admin\FeedbackController::class);
    Route::get('feedback-statistics', [App\Http\Controllers\Admin\FeedbackController::class, 'statistics'])->name('feedback.statistics');
    Route::delete('feedback/bulk-delete', [App\Http\Controllers\Admin\FeedbackController::class, 'bulkDelete'])->name('feedback.bulk-delete');
});
