# Google Cloud Console Setup - Fix "Invalid Request" Error

## 🚨 **FIXING "INVALID REQUEST" ERROR**

The "invalid request" error typically occurs due to misconfiguration in Google Cloud Console. Here's how to fix it:

## **🔧 Step-by-Step Fix:**

### **Step 1: Access Google Cloud Console**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to "APIs & Services" → "Credentials"

### **Step 2: Configure OAuth 2.0 Client**

#### **🔍 Find Your OAuth Client:**
1. Look for your OAuth 2.0 Client ID: `*********************************************.apps.googleusercontent.com`
2. Click on it to edit

#### **🔧 Required Configuration:**

**Authorized JavaScript Origins:**
```
http://127.0.0.1:8008
http://localhost:8008
```

**Authorized Redirect URIs:**
```
http://127.0.0.1:8008/auth/google/callback
http://localhost:8008/auth/google/callback
```

### **Step 3: OAuth Consent Screen**

#### **🔍 Configure Consent Screen:**
1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" (for testing)
3. Fill required fields:
   - **App name**: QuestionCraft
   - **User support email**: Your email
   - **Developer contact**: Your email

#### **📋 Scopes (Add these):**
```
../auth/userinfo.email
../auth/userinfo.profile
openid
```

### **Step 4: Enable Required APIs**

#### **🔧 Enable These APIs:**
1. Go to "APIs & Services" → "Library"
2. Search and enable:
   - **Google+ API** (if available)
   - **People API**
   - **Google Identity Services API**

### **Step 5: Test Users (For Development)**

#### **👥 Add Test Users:**
1. Go to "OAuth consent screen" → "Test users"
2. Add your email address as a test user
3. Add any other emails you want to test with

## **🔍 Common Issues & Solutions:**

### **❌ Issue 1: "redirect_uri_mismatch"**
**Solution:** Ensure redirect URIs in Google Console exactly match:
```
http://127.0.0.1:8008/auth/google/callback
```

### **❌ Issue 2: "invalid_client"**
**Solution:** 
- Verify Client ID and Secret are correct
- Check for extra spaces or characters
- Regenerate credentials if needed

### **❌ Issue 3: "access_blocked"**
**Solution:**
- Add your email as a test user
- Ensure OAuth consent screen is properly configured
- Check app verification status

### **❌ Issue 4: "unauthorized_client"**
**Solution:**
- Enable required APIs (People API, Google+ API)
- Check JavaScript origins are configured
- Verify OAuth client type is "Web application"

## **🔧 Quick Fix Checklist:**

### **✅ Google Cloud Console:**
- [ ] OAuth 2.0 Client created
- [ ] Correct redirect URIs added
- [ ] JavaScript origins configured
- [ ] OAuth consent screen configured
- [ ] Required APIs enabled
- [ ] Test users added (for development)

### **✅ Application Configuration:**
- [ ] Correct Client ID in .env
- [ ] Correct Client Secret in .env
- [ ] Correct redirect URI in .env
- [ ] Configuration cache cleared

## **🔧 Alternative Solution - Create New OAuth Client:**

If issues persist, create a new OAuth client:

### **Step 1: Create New OAuth Client**
1. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client ID"
2. Choose "Web application"
3. Name: "QuestionCraft OAuth"

### **Step 2: Configure New Client**
**Authorized JavaScript origins:**
```
http://127.0.0.1:8008
http://localhost:8008
```

**Authorized redirect URIs:**
```
http://127.0.0.1:8008/auth/google/callback
http://localhost:8008/auth/google/callback
```

### **Step 3: Update Credentials**
Copy the new Client ID and Secret to your .env file.

## **🧪 Testing Steps:**

### **Step 1: Test Configuration**
Visit: `http://127.0.0.1:8008/test-google-oauth`

### **Step 2: Check Response**
Look for:
- `"ready": true`
- `"socialite_works": true`
- `"driver_works": true`

### **Step 3: Test OAuth Flow**
1. Visit: `http://127.0.0.1:8008/login`
2. Click "Continue with Google"
3. Should redirect to Google OAuth (not show error)

## **🔍 Debug Information:**

### **Current Configuration:**
- **Client ID**: `*********************************************.apps.googleusercontent.com`
- **Redirect URI**: `http://127.0.0.1:8008/auth/google/callback`
- **App URL**: `http://127.0.0.1:8008`

### **Required Google Console Settings:**
```json
{
  "web": {
    "client_id": "*********************************************.apps.googleusercontent.com",
    "client_secret": "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",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "redirect_uris": [
      "http://127.0.0.1:8008/auth/google/callback",
      "http://localhost:8008/auth/google/callback"
    ],
    "javascript_origins": [
      "http://127.0.0.1:8008",
      "http://localhost:8008"
    ]
  }
}
```

## **🎯 Next Steps:**

1. **Check Google Console**: Verify all settings match the requirements above
2. **Test Configuration**: Use the test route to verify setup
3. **Test OAuth Flow**: Try the actual login process
4. **Check Browser Console**: Look for any JavaScript errors
5. **Check Server Logs**: Look for any server-side errors

## **📞 If Still Having Issues:**

1. **Double-check all URLs** in Google Console
2. **Try incognito/private browsing** to avoid cache issues
3. **Clear browser cache** completely
4. **Wait 5-10 minutes** after making changes in Google Console
5. **Try creating a completely new OAuth client** with fresh credentials

The "invalid request" error should be resolved once the Google Cloud Console configuration matches exactly with your application settings!
