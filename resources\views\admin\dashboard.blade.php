@extends('layouts.admin')

@section('title', 'Admin Dashboard - Complete Management')
@section('page-title', 'Admin Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Header -->
        <div class="mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 lg:p-8 text-white shadow-xl">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-4 lg:mb-0">
                        <h1 class="text-2xl lg:text-3xl font-bold mb-2">
                            Welcome back, {{ auth()->user()->name ?? session('user.name', 'Admin') }}! 👋
                        </h1>
                        <p class="text-blue-100 text-sm lg:text-base">
                            Complete admin dashboard with CRUD management, analytics, and system monitoring
                        </p>
                    </div>
                    <div class="flex flex-wrap gap-3">
                        <a href="{{ route('admin.reports') }}"
                           class="inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium">
                            <i class="fas fa-chart-line mr-2"></i>Reports
                        </a>
                        <a href="{{ route('admin.export-data') }}"
                           class="inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium">
                            <i class="fas fa-download mr-2"></i>Export
                        </a>
                        <a href="{{ route('admin.system-health') }}"
                           class="inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium">
                            <i class="fas fa-heartbeat mr-2"></i>Health
                        </a>
                        <div class="hidden lg:block ml-4">
                            <div class="text-right">
                                <p class="text-blue-100 text-xs">Last login</p>
                                <p class="text-white font-medium text-sm">{{ now()->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Users -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                            <i class="fas fa-arrow-up mr-1"></i>+{{ $stats['users_growth'] }}%
                        </span>
                    </div>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">Total Users</p>
                    <p class="text-3xl font-bold text-gray-900 mb-2">{{ number_format($stats['total_users']) }}</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Active: {{ $stats['active_users'] }}</span>
                        <span class="text-emerald-600 font-medium">{{ round(($stats['active_users'] / $stats['total_users']) * 100, 1) }}%</span>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-500"
                                 style="width: {{ ($stats['active_users'] / $stats['total_users']) * 100 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Questions -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-14 h-14 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-question-circle text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                            <i class="fas fa-plus mr-1"></i>{{ $stats['questions_today'] }} today
                        </span>
                    </div>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">Total Questions</p>
                    <p class="text-3xl font-bold text-gray-900 mb-2">{{ number_format($stats['total_questions']) }}</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">This week: {{ $stats['questions_week'] }}</span>
                        <span class="text-emerald-600 font-medium">Active</span>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full transition-all duration-500" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Notes -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-sticky-note text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {{ $stats['processing_rate'] }}% published
                        </span>
                    </div>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">Total Notes</p>
                    <p class="text-3xl font-bold text-gray-900 mb-2">{{ number_format($stats['total_documents']) }}</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Processing: {{ $stats['documents_processing'] }}</span>
                        <span class="text-purple-600 font-medium">{{ $stats['processing_rate'] }}%</span>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-600 h-2 rounded-full transition-all duration-500"
                                 style="width: {{ $stats['processing_rate'] }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Performance -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check mr-1"></i>Healthy
                        </span>
                    </div>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">System Status</p>
                    <p class="text-3xl font-bold text-gray-900 mb-2">99.9%</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Uptime</span>
                        <span class="text-green-600 font-medium">Excellent</span>
                    </div>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-500" style="width: 99%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CRUD Management Grid -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Management Center</h2>
                    <p class="text-gray-600">Complete database management and administrative operations</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-database mr-2"></i>{{ $stats['total_users'] + $stats['total_questions'] + $stats['total_documents'] }} Total Records
                    </span>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- User Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-users text-white text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-gray-900">{{ $stats['total_users'] }}</span>
                                <p class="text-sm text-gray-500">Total Users</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">User Management</h3>
                            <p class="text-sm text-gray-600">Manage users, roles, and profiles</p>
                        </div>
                        <div class="space-y-3">
                            <a href="{{ route('admin.users-crud.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-list mr-3 text-blue-600"></i>View All Users
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.users-crud.create') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-plus mr-3 text-blue-600"></i>Add New User
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.user-profiles.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-id-card mr-3 text-blue-600"></i>User Profiles
                                <span class="ml-auto">→</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Subject Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-book text-white text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-gray-900">{{ $stats['subjects']['total'] ?? 10 }}</span>
                                <p class="text-sm text-gray-500">Subjects</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Subject Management</h3>
                            <p class="text-sm text-gray-600">Organize academic subjects and curriculum</p>
                        </div>
                        <div class="space-y-3">
                            <a href="{{ route('admin.subjects.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-purple-50 hover:text-purple-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-list mr-3 text-purple-600"></i>View All Subjects
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.subjects.create') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-purple-50 hover:text-purple-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-plus mr-3 text-purple-600"></i>Add New Subject
                                <span class="ml-auto">→</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Note Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-sticky-note text-white text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-gray-900">{{ $stats['total_documents'] }}</span>
                                <p class="text-sm text-gray-500">Notes</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Note Management</h3>
                            <p class="text-sm text-gray-600">Manage educational content and materials</p>
                        </div>
                        <div class="space-y-3">
                            <a href="{{ route('admin.notes-crud.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-green-50 hover:text-green-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-list mr-3 text-green-600"></i>View All Notes
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.notes-crud.create') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-green-50 hover:text-green-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-plus mr-3 text-green-600"></i>Add New Note
                                <span class="ml-auto">→</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Question Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-question-circle text-white text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-gray-900">{{ $stats['total_questions'] }}</span>
                                <p class="text-sm text-gray-500">Questions</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Question Management</h3>
                            <p class="text-sm text-gray-600">Manage Q&A system and assessments</p>
                        </div>
                        <div class="space-y-3">
                            <a href="{{ route('admin.questions.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-yellow-50 hover:text-yellow-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-list mr-3 text-yellow-600"></i>View All Questions
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.questions.create') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-yellow-50 hover:text-yellow-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-plus mr-3 text-yellow-600"></i>Add New Question
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.answers.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-yellow-50 hover:text-yellow-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-check-circle mr-3 text-yellow-600"></i>Manage Answers
                                <span class="ml-auto">→</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Feedback Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-comments text-white text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-gray-900">{{ $stats['feedback']['total'] ?? 7 }}</span>
                                <p class="text-sm text-gray-500">Feedback</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Feedback Management</h3>
                            <p class="text-sm text-gray-600">Monitor user feedback and ratings</p>
                        </div>
                        <div class="space-y-3">
                            <a href="{{ route('admin.feedback.index') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-list mr-3 text-red-600"></i>View All Feedback
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.feedback.statistics') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-chart-bar mr-3 text-red-600"></i>Feedback Statistics
                                <span class="ml-auto">→</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Tools -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-tools text-white text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>Active
                                </span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">System Tools</h3>
                            <p class="text-sm text-gray-600">Administrative tools and utilities</p>
                        </div>
                        <div class="space-y-3">
                            <a href="{{ route('admin.system-health') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-heartbeat mr-3 text-indigo-600"></i>System Health
                                <span class="ml-auto">→</span>
                            </a>
                            <a href="{{ route('admin.export-data') }}"
                               class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200">
                                <i class="fas fa-download mr-3 text-indigo-600"></i>Export Data
                                <span class="ml-auto">→</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity & Quick Stats -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Recent Activity -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                        <button class="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center">
                            <i class="fas fa-sync-alt mr-1"></i>Refresh
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @if(isset($recentActivity) && count($recentActivity) > 0)
                            @foreach($recentActivity->take(5) as $activity)
                                <div class="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-colors duration-200">
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0
                                        @if($activity['type'] === 'user') bg-blue-100
                                        @elseif($activity['type'] === 'question') bg-emerald-100
                                        @elseif($activity['type'] === 'document') bg-purple-100
                                        @else bg-gray-100 @endif">
                                        @if($activity['type'] === 'user')
                                            <i class="fas fa-user text-blue-600"></i>
                                        @elseif($activity['type'] === 'question')
                                            <i class="fas fa-question-circle text-emerald-600"></i>
                                        @elseif($activity['type'] === 'document')
                                            <i class="fas fa-file-alt text-purple-600"></i>
                                        @else
                                            <i class="fas fa-info-circle text-gray-600"></i>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ $activity['user'] }} {{ strtolower($activity['action']) }}
                                        </p>
                                        <p class="text-sm text-gray-500 truncate">{{ $activity['details'] }}</p>
                                        <p class="text-xs text-gray-400">{{ $activity['time']->diffForHumans() }}</p>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-clock text-gray-400 text-3xl mb-3"></i>
                                <p class="text-gray-500">No recent activity</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100">
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">System Status</h3>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm text-green-600 font-medium">All Systems Operational</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        @if(isset($systemHealth))
                            @foreach($systemHealth as $service)
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-sm font-medium text-gray-900">{{ $service['service'] }}</span>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-xs text-gray-500">{{ $service['uptime'] }}</p>
                                        <p class="text-xs text-green-600">{{ $service['response_time'] }}</p>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="col-span-full text-center py-4">
                                <i class="fas fa-server text-gray-400 text-2xl mb-2"></i>
                                <p class="text-gray-500">System monitoring data unavailable</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Info -->
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-1">QuestionCraft Admin Dashboard</h4>
                    <p class="text-sm text-gray-600">
                        Complete administrative control panel with real-time monitoring and management capabilities
                    </p>
                </div>
                <div class="mt-4 sm:mt-0 flex flex-wrap gap-3">
                    <a href="{{ route('admin.reports') }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-chart-line mr-2"></i>View Reports
                    </a>
                    <a href="{{ route('admin.system-health') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        <i class="fas fa-heartbeat mr-2"></i>System Health
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Unified Admin Dashboard with Complete Management
    console.log('Unified Admin Dashboard loaded with CRUD, Analytics, and System Monitoring');

    // Auto-refresh dashboard data every 30 seconds
    setInterval(function() {
        console.log('Dashboard data refresh...');
    }, 30000);

    // Add hover effects to metric cards
    const metricCards = document.querySelectorAll('.transform');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Animate progress bars on load
    const progressBars = document.querySelectorAll('[style*="width:"]');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
            bar.style.transition = 'width 1s ease-in-out';
        }, 500);
    });

    console.log('Complete admin dashboard ready with all functionality');
});
</script>
@endsection

