# Google Console Setup for Port 8000 - REQUIRED

## 🚨 **REDIRECT URI MISMATCH ERROR - SOLUTION**

**Error Message:**
```
Error 400: redirect_uri_mismatch
Request details: redirect_uri=http://127.0.0.1:8000/auth/google/callback
```

**Root Cause:** Google Console is not configured with the correct redirect URI for port 8000.

## **🔧 REQUIRED GOOGLE CLOUD CONSOLE SETUP:**

### **Step 1: Access Google Cloud Console**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services** → **Credentials**
3. Find your OAuth client: `680117937871-j77mk3jcojvmcgg1d3jnv742sn3leemm.apps.googleusercontent.com`
4. Click to **EDIT** the OAuth client

### **Step 2: Configure Authorized Redirect URIs**
**Add these EXACT redirect URIs:**
```
http://127.0.0.1:8000/auth/google/callback
http://localhost:8000/auth/google/callback
```

### **Step 3: Configure Authorized JavaScript Origins**
**Add these EXACT JavaScript origins:**
```
http://127.0.0.1:8000
http://localhost:8000
```

### **Step 4: OAuth Consent Screen Configuration**
1. Go to **APIs & Services** → **OAuth consent screen**
2. Configure:
   - **User Type**: External
   - **App name**: Smart Study AI (or your preferred name)
   - **User support email**: Your email address
   - **Developer contact information**: Your email address

### **Step 5: Add Test Users**
1. In OAuth consent screen → **Test users**
2. Click **ADD USERS**
3. Add your email address as a test user

### **Step 6: Enable Required APIs**
1. Go to **APIs & Services** → **Library**
2. Search and enable:
   - **People API**
   - **Google Identity Services API**

## **✅ EXACT CONFIGURATION NEEDED:**

### **🔑 OAuth Client Configuration:**
- **Client ID**: `680117937871-j77mk3jcojvmcgg1d3jnv742sn3leemm.apps.googleusercontent.com`
- **Client Secret**: `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`

### **📍 Authorized Redirect URIs (COPY EXACTLY):**
```
http://127.0.0.1:8000/auth/google/callback
http://localhost:8000/auth/google/callback
```

### **🌐 Authorized JavaScript Origins (COPY EXACTLY):**
```
http://127.0.0.1:8000
http://localhost:8000
```

## **⚠️ IMPORTANT NOTES:**

### **1. Exact Match Required:**
- URLs must match **EXACTLY** (including protocol, domain, port, and path)
- `http://127.0.0.1:8000` ≠ `http://localhost:8000` (both needed)
- No trailing slashes in origins
- Include trailing path in redirect URIs

### **2. Propagation Time:**
- Changes take **10-15 minutes** to propagate
- Wait after making changes before testing

### **3. Testing Mode:**
- App must be in **Testing** mode (not Production)
- Your email must be added as a **Test User**

## **🧪 TESTING STEPS AFTER SETUP:**

### **Step 1: Wait 15 Minutes**
After making changes in Google Console, wait 15 minutes for propagation.

### **Step 2: Clear Browser Cache**
- Clear all browser cache and cookies
- Or use incognito/private browsing mode

### **Step 3: Test OAuth Flow**
1. Visit: `http://127.0.0.1:8000/login`
2. Click: "Continue with Google" button
3. Should redirect to Google OAuth (no error)
4. Complete authentication
5. Should redirect back to dashboard

### **Step 4: Verify Success**
- User should be logged in
- Should see dashboard page
- No redirect URI mismatch error

## **🔍 TROUBLESHOOTING:**

### **If Still Getting redirect_uri_mismatch:**
1. **Double-check URLs** in Google Console match exactly
2. **Wait longer** (up to 30 minutes for propagation)
3. **Clear browser cache** completely
4. **Try incognito mode**
5. **Verify OAuth consent screen** is configured
6. **Check test users** include your email

### **If Getting "invalid_client":**
1. **Verify Client ID** is correct in .env
2. **Check Client Secret** is correct in .env
3. **Ensure APIs are enabled** (People API, Google Identity Services API)

### **If Getting "access_blocked":**
1. **Add your email** as a test user
2. **Configure OAuth consent screen** completely
3. **Ensure app is in Testing mode** (not Production)

## **📋 VERIFICATION CHECKLIST:**

- [ ] OAuth client found in Google Console
- [ ] Redirect URIs added: `http://127.0.0.1:8000/auth/google/callback`
- [ ] Redirect URIs added: `http://localhost:8000/auth/google/callback`
- [ ] JavaScript origins added: `http://127.0.0.1:8000`
- [ ] JavaScript origins added: `http://localhost:8000`
- [ ] OAuth consent screen configured
- [ ] Test users added (your email)
- [ ] People API enabled
- [ ] Google Identity Services API enabled
- [ ] Waited 15+ minutes after changes
- [ ] Cleared browser cache
- [ ] Tested in incognito mode

## **🎯 EXPECTED RESULT:**

After proper Google Console configuration:
- ✅ No "redirect_uri_mismatch" error
- ✅ Google OAuth consent screen appears
- ✅ User can authenticate with Google
- ✅ User gets redirected back to application
- ✅ User is logged in and sees dashboard

## **📞 NEXT STEPS:**

1. **Configure Google Console** using the exact URLs above
2. **Wait 15 minutes** for changes to propagate
3. **Clear browser cache** or use incognito mode
4. **Test OAuth flow** again
5. **Report results** - should work without redirect_uri_mismatch error

**The application is correctly configured for port 8000. The issue is purely in the Google Console configuration that needs to match these exact URLs! 🎯🔐✨**
