<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo $__env->yieldContent('title', 'QuestionCraft - Transform Learning into Questions'); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

        <!-- Styles / Scripts -->
        <?php if(file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot'))): ?>
            <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
        <?php else: ?>
            <style>
                /* Fallback styles */
                body { font-family: 'Inter', sans-serif; }
            </style>
        <?php endif; ?>

        <?php echo $__env->yieldPushContent('styles'); ?>
    </head>
    <body class="bg-gray-50 font-sans">
        <!-- Simple Header for Auth Pages -->
        <header class="bg-white border-b border-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="<?php echo e(url('/')); ?>" class="text-2xl font-bold text-gray-900">QuestionCraft</a>
                    </div>
                    
                    <!-- Back to Home -->
                    <div>
                        <a href="<?php echo e(url('/')); ?>" class="text-gray-700 hover:text-gray-900 text-sm">
                            ← Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main>
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        <?php echo $__env->yieldPushContent('scripts'); ?>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\StudySmartAI_New\smart-study-ai\resources\views/layouts/auth.blade.php ENDPATH**/ ?>