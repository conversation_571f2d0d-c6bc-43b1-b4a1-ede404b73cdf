<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class GoogleController extends Controller
{
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            // Get user from Google
            $googleUser = Socialite::driver('google')->user();

            // Log for debugging
            \Log::info('Google OAuth Callback', [
                'google_user_id' => $googleUser->getId(),
                'google_user_email' => $googleUser->getEmail(),
                'google_user_name' => $googleUser->getName(),
            ]);

            // Find or create user
            $user = User::where('email', $googleUser->getEmail())->first();

            if ($user) {
                // Update existing user
                $user->update([
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                ]);
                \Log::info('Updated existing user', ['user_id' => $user->user_id]);
            } else {
                // Create new user
                $user = User::create([
                    'user_id' => (string) Str::uuid(),
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'username' => $this->generateUsername($googleUser->getName()),
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                    'password' => Hash::make(Str::random(24)),
                    'role' => 'student',
                    'is_active' => true,
                ]);
                \Log::info('Created new user', ['user_id' => $user->user_id]);
            }

            // Login user
            Auth::login($user);
            \Log::info('User logged in', ['auth_check' => Auth::check(), 'auth_id' => Auth::id()]);

            // Store user data in session
            session([
                'user' => [
                    'id' => $user->user_id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'avatar' => $user->avatar,
                ]
            ]);

            // Redirect to dashboard with success message
            return redirect()->route('dashboard')->with('success', 'Welcome, ' . $user->name . '!');

        } catch (\Exception $e) {
            \Log::error('Google OAuth Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect('/login')->with('error', 'Google authentication failed: ' . $e->getMessage());
        }
    }

    private function generateUsername($name)
    {
        $username = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $name));

        if (strlen($username) < 3) {
            $username = 'user' . $username;
        }

        if (strlen($username) > 20) {
            $username = substr($username, 0, 20);
        }

        $counter = 1;
        $originalUsername = $username;
        while (User::where('username', $username)->exists()) {
            $username = $originalUsername . $counter;
            $counter++;
        }

        return $username;
    }
}
