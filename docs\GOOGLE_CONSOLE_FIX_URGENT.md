# 🚨 URGENT: Google Console Configuration Fix

## **CURRENT ERROR:**
```
Error 400: redirect_uri_mismatch
redirect_uri=http://127.0.0.1:8000/auth/google/callback
client_id=************-j77mk3jcojvmcgg1d3jnv742sn3leemm.apps.googleusercontent.com
```

## **🎯 EXACT SOLUTION NEEDED:**

### **Step 1: Access Google Cloud Console**
1. **Open**: https://console.cloud.google.com/
2. **Login** with your Google account
3. **Select** your project (if you have multiple)

### **Step 2: Navigate to OAuth Settings**
1. **Click**: "APIs & Services" in left sidebar
2. **Click**: "Credentials"
3. **Find**: OAuth 2.0 Client ID with ID: `************-j77mk3jcojvmcgg1d3jnv742sn3leemm.apps.googleusercontent.com`
4. **Click**: The pencil/edit icon next to it

### **Step 3: Add Redirect URIs (CRITICAL)**
In the "Authorized redirect URIs" section:
1. **Click**: "+ ADD URI"
2. **Type EXACTLY**: `http://127.0.0.1:8000/auth/google/callback`
3. **Click**: "+ ADD URI" again
4. **Type EXACTLY**: `http://localhost:8000/auth/google/callback`

### **Step 4: Add JavaScript Origins**
In the "Authorized JavaScript origins" section:
1. **Click**: "+ ADD URI"
2. **Type EXACTLY**: `http://127.0.0.1:8000`
3. **Click**: "+ ADD URI" again
4. **Type EXACTLY**: `http://localhost:8000`

### **Step 5: Save Changes**
1. **Click**: "SAVE" button at the bottom
2. **Wait**: For confirmation message

## **⚠️ CRITICAL REQUIREMENTS:**

### **URLs Must Be EXACT:**
- ✅ `http://127.0.0.1:8000/auth/google/callback`
- ✅ `http://localhost:8000/auth/google/callback`
- ✅ `http://127.0.0.1:8000`
- ✅ `http://localhost:8000`

### **Common Mistakes to AVOID:**
- ❌ `https://` (must be `http://`)
- ❌ Missing port `:8000`
- ❌ Wrong port (like `:8008`)
- ❌ Trailing slashes on origins
- ❌ Missing `/auth/google/callback` path

## **🕐 TIMING:**

### **Propagation Time:**
- **Minimum**: 5-10 minutes
- **Typical**: 10-15 minutes
- **Maximum**: 30 minutes

### **After Making Changes:**
1. **Wait**: 15 minutes minimum
2. **Clear**: Browser cache completely
3. **Use**: Incognito/private browsing mode
4. **Test**: OAuth flow again

## **🧪 TESTING PROCEDURE:**

### **After 15 Minutes Wait:**
1. **Visit**: `http://127.0.0.1:8000/test-config`
2. **Verify**: Configuration shows correct URLs
3. **Visit**: `http://127.0.0.1:8000/login`
4. **Click**: "Continue with Google"
5. **Should**: Redirect to Google (no error)

## **🔍 ADDITIONAL SETUP (If Needed):**

### **OAuth Consent Screen:**
1. **Go to**: APIs & Services → OAuth consent screen
2. **Set**: User Type to "External"
3. **Fill**: App name (e.g., "Smart Study AI")
4. **Add**: Your email as support email
5. **Save**: Changes

### **Test Users:**
1. **In OAuth consent screen**: Go to "Test users"
2. **Click**: "ADD USERS"
3. **Add**: Your email address
4. **Save**: Changes

### **Enable APIs:**
1. **Go to**: APIs & Services → Library
2. **Search**: "People API"
3. **Click**: "ENABLE"
4. **Search**: "Google Identity Services API"
5. **Click**: "ENABLE"

## **📋 VERIFICATION CHECKLIST:**

Before testing, ensure:
- [ ] OAuth client found in Google Console
- [ ] `http://127.0.0.1:8000/auth/google/callback` added to redirect URIs
- [ ] `http://localhost:8000/auth/google/callback` added to redirect URIs
- [ ] `http://127.0.0.1:8000` added to JavaScript origins
- [ ] `http://localhost:8000` added to JavaScript origins
- [ ] Changes saved in Google Console
- [ ] Waited 15+ minutes after saving
- [ ] Browser cache cleared
- [ ] Testing in incognito mode

## **🎯 EXPECTED RESULT:**

After proper configuration:
- ✅ No "redirect_uri_mismatch" error
- ✅ Google OAuth consent screen appears
- ✅ User can authenticate
- ✅ Redirects back to application
- ✅ User logged in successfully

## **📞 IMMEDIATE ACTION REQUIRED:**

**RIGHT NOW:**
1. **Open**: https://console.cloud.google.com/
2. **Find**: OAuth client `************-j77mk3jcojvmcgg1d3jnv742sn3leemm.apps.googleusercontent.com`
3. **Add**: `http://127.0.0.1:8000/auth/google/callback` to redirect URIs
4. **Add**: `http://127.0.0.1:8000` to JavaScript origins
5. **Save**: Changes
6. **Wait**: 15 minutes
7. **Test**: OAuth flow again

**The application code is perfect. The only issue is the Google Console configuration! 🎯🔐✨**
